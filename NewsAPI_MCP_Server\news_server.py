# news_server.py
import os
import logging
from datetime import datetime, timedelta
from typing import Optional
from pydantic import BaseModel, Field

import httpx
from fastmcp import FastMCP
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP("Geospatial News MCP Server")

# NewsAPI configuration
NEWS_API_KEY = os.getenv("NEWSAPI_APIKEY")
NEWS_API_BASE_URL = "https://newsapi.org/v2"

if not NEWS_API_KEY:
    raise ValueError("NEWSAPI_APIKEY environment variable is required")

# Pydantic models for type safety and validation
class NewsSearchRequest(BaseModel):
    """Request model for news search with validation"""
    query: str = Field(description="Keywords or phrases to search for")
    sources: Optional[str] = Field(None, description="Comma-separated list of news sources (e.g., 'bbc-news,cnn')")
    language: str = Field("en", description="Language code (default: en)")
    from_date: Optional[str] = Field(None, description="Start date (YYYY-MM-DD format)")
    to_date: Optional[str] = Field(None, description="End date (YYYY-MM-DD format)")
    sort_by: str = Field("publishedAt", description="Sort by: relevancy, popularity, or publishedAt")
    page_size: int = Field(20, ge=1, le=100, description="Number of results (1-100)")
    page: int = Field(1, ge=1, description="Page number for pagination")

class HeadlinesRequest(BaseModel):
    """Request model for top headlines"""
    country: Optional[str] = Field(None, description="2-letter ISO 3166-1 country code (e.g., 'us', 'gb', 'ca')")
    category: Optional[str] = Field(None, description="Category: business, entertainment, general, health, science, sports, technology")
    sources: Optional[str] = Field(None, description="Comma-separated list of news sources")
    query: Optional[str] = Field(None, description="Keywords to search in headlines")
    page_size: int = Field(20, ge=1, le=100, description="Number of results (1-100)")
    page: int = Field(1, ge=1, description="Page number for pagination")

class SourcesRequest(BaseModel):
    """Request model for news sources"""
    country: Optional[str] = Field(None, description="2-letter country code")
    category: Optional[str] = Field(None, description="News category")
    language: Optional[str] = Field(None, description="Language code")

# HTTP client for NewsAPI requests
async def make_news_request(endpoint: str, params: dict) -> dict:
    """Make authenticated request to NewsAPI"""
    params["apiKey"] = NEWS_API_KEY
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.get(f"{NEWS_API_BASE_URL}/{endpoint}", params=params)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"NewsAPI request failed: {e}")
            raise Exception(f"NewsAPI error: {str(e)}")

def format_articles_response(data: dict, search_type: str) -> str:
    """Format articles response for readability"""
    if data["status"] != "ok":
        return f"Error: {data.get('message', 'Unknown error')}"
    
    articles = data["articles"]
    total_results = data.get("totalResults", len(articles))
    
    response = f"{search_type.title()} Results (Found {total_results} articles):\n\n"
    
    for i, article in enumerate(articles, 1):
        response += f"Article {i}:\n"
        response += f"Title: {article['title']}\n"
        response += f"Source: {article['source']['name']}\n"
        response += f"Author: {article.get('author', 'Unknown')}\n"
        response += f"Published: {article['publishedAt']}\n"
        response += f"Description: {article.get('description', 'No description')}\n"
        response += f"URL: {article['url']}\n"
        response += "─" * 50 + "\n"
    
    return response

def format_sources_response(data: dict) -> str:
    """Format sources response for readability"""
    if data["status"] != "ok":
        return f"Error: {data.get('message', 'Unknown error')}"
    
    sources = data["sources"]
    response = f"Available News Sources (Found {len(sources)} sources):\n\n"
    
    for i, source in enumerate(sources, 1):
        response += f"Source {i}:\n"
        response += f"Name: {source['name']}\n"
        response += f"ID: {source['id']}\n"
        response += f"Description: {source.get('description', 'No description')}\n"
        response += f"Category: {source.get('category', 'general')}\n"
        response += f"Language: {source.get('language', 'en')}\n"
        response += f"Country: {source.get('country', 'N/A')}\n"
        response += f"URL: {source['url']}\n"
        response += "─" * 40 + "\n"
    
    return response

# MCP Tools using FastMCP decorators
@mcp.tool()
async def search_geospatial_news(request: NewsSearchRequest) -> str:
    """
    Search for news articles with keyword filtering.
    Perfect for geospatial intelligence gathering and monitoring specific topics or locations.
    """
    params = {
        "q": request.query,
        "language": request.language,
        "sortBy": request.sort_by,
        "pageSize": request.page_size,
        "page": request.page,
    }
    
    # Add optional parameters
    if request.sources:
        params["sources"] = request.sources
    if request.from_date:
        params["from"] = request.from_date
    if request.to_date:
        params["to"] = request.to_date
    
    # Set default time window for recent news (last 24 hours if no dates specified)
    if not request.from_date and not request.to_date:
        from_date = (datetime.utcnow() - timedelta(hours=24)).strftime("%Y-%m-%d")
        params["from"] = from_date
    
    data = await make_news_request("everything", params)
    return format_articles_response(data, "geospatial news search")

@mcp.tool()
async def get_regional_headlines(request: HeadlinesRequest) -> str:
    """
    Get top headlines for specific countries or categories.
    Ideal for hyperlocal intelligence and regional monitoring.
    """
    params = {
        "pageSize": request.page_size,
        "page": request.page,
    }
    
    # Add optional parameters
    if request.country:
        params["country"] = request.country
    if request.category:
        params["category"] = request.category
    if request.sources:
        params["sources"] = request.sources
    if request.query:
        params["q"] = request.query
    
    data = await make_news_request("top-headlines", params)
    return format_articles_response(data, "regional headlines")

@mcp.tool()
async def get_news_sources(request: SourcesRequest) -> str:
    """
    Get available news sources with geographic and category filtering.
    Useful for understanding source coverage and planning data collection strategies.
    """
    params = {}
    
    # Add optional parameters
    if request.country:
        params["country"] = request.country
    if request.category:
        params["category"] = request.category
    if request.language:
        params["language"] = request.language
    
    data = await make_news_request("top-headlines/sources", params)
    return format_sources_response(data)

# Resource for API information
@mcp.resource("news://api-info")
async def get_api_info() -> str:
    """
    Information about the NewsAPI integration and usage limits.
    """
    return """
# NewsAPI Integration Information

## Current Configuration
- API Endpoint: https://newsapi.org/v2
- Authentication: API Key configured
- Rate Limits: 100 requests/day (free tier)

## Available Countries
- US (us), GB (gb), CA (ca), AU (au), DE (de), FR (fr), IN (in), JP (jp)
- And many more - see NewsAPI documentation for full list

## Available Categories
- business, entertainment, general, health, science, sports, technology

## Language Codes
- en (English), es (Spanish), fr (French), de (German), it (Italian)
- ar (Arabic), zh (Chinese), ru (Russian), pt (Portuguese), nl (Dutch)

## Best Practices
- Use specific keywords for better results
- Combine location and topic for geospatial intelligence
- Set date ranges to focus on recent developments
- Use country codes for regional monitoring

## Rate Limit Management
- Free tier: 100 requests/day
- Consider upgrading for production use
- Cache results when possible to reduce API calls
"""

if __name__ == "__main__":
    # Run the MCP server with STDIO transport for Claude Desktop integration
    # For HTTP testing, you can change to: mcp.run(transport="http", port=8000, host="localhost")
    mcp.run()