# internet_search_server.py
import os
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

import httpx
from fastmcp import FastMC<PERSON>
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP("Geospatial Internet Search MCP Server")

# Tavily API configuration
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")
TAVILY_API_BASE_URL = "https://api.tavily.com"

if not TAVILY_API_KEY:
    raise ValueError("TAVILY_API_KEY environment variable is required")

# Pydantic models for type safety and validation
class InternetSearchRequest(BaseModel):
    """Request model for internet search with validation"""
    keywords: List[str] = Field(description="Search keywords list")
    location_scope: Optional[str] = Field(None, description="Geographic scope for search (e.g., 'United States', 'Europe')")
    max_results: int = Field(10, ge=1, le=20, description="Maximum number of results (1-20)")
    search_depth: str = Field("basic", description="Search depth: 'basic' or 'advanced'")
    include_domains: Optional[List[str]] = Field(None, description="Specific domains to include in search")
    exclude_domains: Optional[List[str]] = Field(None, description="Specific domains to exclude from search")
    include_raw_content: bool = Field(True, description="Whether to include raw content in response")

class TopicExtractionRequest(BaseModel):
    """Request model for topic extraction from search results"""
    query: str = Field(description="Original search query")
    content: str = Field(description="Raw content to extract topics from")
    max_topics: int = Field(5, ge=1, le=10, description="Maximum number of topics to extract")

class WebSourcesRequest(BaseModel):
    """Request model for discovering web sources"""
    domain_pattern: Optional[str] = Field(None, description="Domain pattern to search for (e.g., '.gov', '.edu')")
    topic_filter: Optional[str] = Field(None, description="Topic filter for source discovery")
    location_filter: Optional[str] = Field(None, description="Location-based filter for sources")

# HTTP client for Tavily API requests
async def make_tavily_request(endpoint: str, payload: dict) -> dict:
    """Make authenticated request to Tavily API"""
    headers = {
        "Authorization": f"Bearer {TAVILY_API_KEY}",
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.post(f"{TAVILY_API_BASE_URL}/{endpoint}", json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"Tavily API request failed: {e}")
            raise Exception(f"Tavily API error: {str(e)}")

def format_search_results(data: dict, search_type: str) -> str:
    """Format Tavily search results for Deep Research MCP consumption"""
    if "results" not in data:
        return f"Error: No results found in Tavily response"
    
    results = data["results"]
    query = data.get("query", "Unknown query")
    
    response = f"🔍 {search_type.title()} Results for '{query}' (Found {len(results)} results):\n\n"
    
    for i, result in enumerate(results, 1):
        response += f"Raw Document {i}:\n"
        response += f"Title: {result.get('title', 'No title')}\n"
        response += f"URL: {result.get('url', 'No URL')}\n"
        response += f"Content: {result.get('content', 'No content')[:500]}...\n"
        response += f"Relevance Score: {result.get('score', 0):.3f}\n"
        
        # Add published date if available
        if 'published_date' in result:
            response += f"Published: {result['published_date']}\n"
        
        # Add domain information for source credibility
        domain = result.get('url', '').replace('https://', '').replace('http://', '').split('/')[0]
        response += f"Domain: {domain}\n"
        
        response += "─" * 50 + "\n"
    
    return response

def format_topics_response(topics: List[str], query: str) -> str:
    """Format extracted topics for Deep Research MCP analysis"""
    response = f"Topics Extracted from '{query}':\n\n"
    
    for i, topic in enumerate(topics, 1):
        response += f"{i}. {topic}\n"
    
    response += f"\n Ready for Deep Research MCP processing"
    return response

def format_sources_response(sources: List[dict], filter_type: str) -> str:
    """Format discovered sources for monitoring and data collection"""
    response = f" Web Sources Discovery ({filter_type}) - Found {len(sources)} sources:\n\n"
    
    for i, source in enumerate(sources, 1):
        response += f"Source {i}:\n"
        response += f"Domain: {source.get('domain', 'Unknown')}\n"
        response += f"Type: {source.get('type', 'Unknown')}\n"
        response += f"Credibility: {source.get('credibility_score', 'N/A')}\n"
        response += f"Description: {source.get('description', 'No description')}\n"
        response += "─" * 40 + "\n"
    
    return response

# MCP Tools using FastMCP decorators (hyphenated naming convention)
@mcp.tool()
async def search_internet_content(request: InternetSearchRequest) -> str:
    """
    Search internet content using Tavily API for raw documents and basic information.
    Provides foundational web search results for Deep Research MCP processing.
    No sentiment analysis - focus on raw content delivery.
    """
    # Build search query from keywords
    query = " ".join(request.keywords)
    
    # Add location scope if specified
    if request.location_scope:
        query = f"{query} {request.location_scope}"
    
    # Build Tavily search payload
    payload = {
        "query": query,
        "max_results": request.max_results,
        "search_depth": request.search_depth,
        "include_raw_content": request.include_raw_content,
        "include_images": False,  # Focus on text content for geospatial intelligence
        "include_answer": False,  # Raw results only, no Tavily summarization
    }
    
    # Add optional filters
    if request.include_domains:
        payload["include_domains"] = request.include_domains
    
    if request.exclude_domains:
        payload["exclude_domains"] = request.exclude_domains
    
    data = await make_tavily_request("search", payload)
    return format_search_results(data, "internet content search")

@mcp.tool()
async def extract_web_topics(request: TopicExtractionRequest) -> str:
    """
    Extract topics from web content for trend identification.
    Provides basic topic clusters for Deep Research MCP analysis.
    No complex NLP - simple keyword-based extraction for speed.
    """
    # Simple keyword-based topic extraction for speed (20-40ms target)
    keywords = request.content.lower().split()
    common_words = {
        'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 
        'is', 'are', 'was', 'were', 'a', 'an', 'this', 'that', 'will', 'would', 'could',
        'have', 'has', 'had', 'been', 'being', 'do', 'does', 'did', 'can', 'may', 'might'
    }
    
    # Count word frequency for topic extraction
    word_freq = {}
    for word in keywords:
        # Filter out short words and common words
        if len(word) > 3 and word not in common_words and word.isalpha():
            word_freq[word] = word_freq.get(word, 0) + 1
    
    # Get top topics based on frequency
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    topics = [word.title() for word, freq in sorted_words[:request.max_topics] if freq > 1]
    
    # If no frequent words found, use query terms
    if not topics:
        topics = [word.title() for word in request.query.split() if len(word) > 3][:request.max_topics]
    
    return format_topics_response(topics, request.query)

@mcp.tool()
async def discover_web_sources(request: WebSourcesRequest) -> str:
    """
    Discover and catalog web sources for comprehensive coverage.
    Identifies potential sources for ongoing monitoring and data collection.
    Focuses on source credibility and domain authority.
    """
    # Build discovery query
    discovery_query = "sources news information"
    
    if request.topic_filter:
        discovery_query += f" {request.topic_filter}"
    
    if request.location_filter:
        discovery_query += f" {request.location_filter}"
    
    # Use domain pattern for targeted discovery
    if request.domain_pattern:
        discovery_query += f" site:{request.domain_pattern}"
    
    payload = {
        "query": discovery_query,
        "max_results": 10,
        "search_depth": "basic",
        "include_raw_content": False,
        "include_answer": False,
    }
    
    # Add domain pattern if specified
    if request.domain_pattern:
        payload["include_domains"] = [request.domain_pattern]
    
    data = await make_tavily_request("search", payload)
    
    # Convert search results to source catalog format
    sources = []
    for result in data.get("results", []):
        url = result.get("url", "")
        domain = url.replace("https://", "").replace("http://", "").split("/")[0]
        
        # Basic credibility scoring based on domain type
        credibility_score = 0.5  # default
        if domain.endswith('.gov'):
            credibility_score = 0.9
        elif domain.endswith('.edu'):
            credibility_score = 0.8
        elif domain.endswith('.org'):
            credibility_score = 0.7
        elif any(news_site in domain for news_site in ['bbc', 'reuters', 'ap', 'bloomberg']):
            credibility_score = 0.8
        
        source = {
            "domain": domain,
            "type": "web",
            "credibility_score": f"{credibility_score:.1f}",
            "description": result.get("content", "")[:200] + "..." if result.get("content") else "No description"
        }
        sources.append(source)
    
    filter_desc = f"topic: {request.topic_filter}" if request.topic_filter else "general discovery"
    return format_sources_response(sources, filter_desc)

# Resource for API information
@mcp.resource("tavily://api-info")
async def get_tavily_info() -> str:
    """
    Information about the Tavily API integration and capabilities.
    """
    return """
# Tavily Internet Search Integration

## Current Configuration
- API Endpoint: https://api.tavily.com
- Authentication: API Key configured
- Target Latency: 20-40ms (per Section 5.1)
- Search Depth: Basic and Advanced modes available
- Rate Limits: Varies by plan (check Tavily dashboard)

## Architecture Role
- Provides RAW web search results only
- NO sentiment analysis (handled by Deep Research MCP)
- Feeds raw documents to Deep Research MCP for processing
- Basic topic extraction for trend identification
- Real-time refresh capability

## Search Capabilities
- Keywords-based web search with location scoping
- Raw content extraction for Deep Research processing
- Domain filtering (include/exclude)
- Relevance scoring (0.0-1.0)
- Real-time web crawling
- Geographic scope filtering for hyperlocal intelligence

## Content Types Delivered
- Raw text content (primary focus)
- URL and domain metadata
- Publication timestamps when available
- Relevance scoring for prioritization
- Basic topic extraction (keyword-based)

## Integration with Deep Research MCP
- Provides raw_documents[] for Deep Research analysis
- Basic topics[] for trend identification
- No complex NLP processing (kept in Deep Research)
- Fast extraction optimized for 20-40ms latency target

## Best Practices for Geospatial Intelligence
- Use location_scope for hyperlocal intelligence
- Combine keywords with geographic terms
- Filter domains for source credibility (.gov, .edu prioritized)
- Limit max_results to maintain speed targets
- Cache results at country-level (per Section 4.2)

## Rate Limit Management
- Monitor API usage in Tavily dashboard
- Implement exponential backoff for failures
- Circuit breaker patterns for resilience
- Consider upgrading plan for production use
- Target <20-40ms response times per SLI requirements
"""

if __name__ == "__main__":
    # Run the MCP server with STDIO transport for Claude Desktop integration
    # For HTTP testing, you can change to: mcp.run(transport="http", port=8001, host="localhost")
    mcp.run()