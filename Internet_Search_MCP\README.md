# Geospatial Internet Search MCP Server

A FastMCP-based internet search server using **Tavily API** for the Geospatial intelligence platform. Provides raw web search results and basic topic extraction for Deep Research MCP processing.

## 🎯 Architecture Role

This server serves as the **Internet Search MCP** component in the Geospatial system architecture:

- **Provider**: Tavily API for web search
- **Input**: `keywords[]`, [location_scope](file://e:\Codecademy\Geospatial%20MCP%20Server\internet_search_server.py#L32-L32) 
- **Output**: `raw_documents[]`, `topics[]`
- **Refresh**: Real-time capability
- **Target Latency**: 20-40ms (per Section 5.1 requirements)

### Key Architectural Principles
- ✅ **Raw search results only** - No sentiment analysis (handled by Deep Research MCP)
- ✅ **Basic topic extraction** - Keyword-based for speed optimization
- ✅ **Location-aware filtering** - Geographic scoping for hyperlocal intelligence
- ✅ **Source credibility scoring** - Domain authority assessment (.gov, .edu prioritized)

## ✨ Features

### **🔍 search-internet-content**
- Keyword-based web search with location scoping
- Raw document extraction for Deep Research processing
- Domain filtering (include/exclude lists)
- Relevance scoring and credibility assessment

### **📊 extract-web-topics** 
- Fast keyword-based topic extraction (optimized for 20-40ms target)
- Basic trend identification for Deep Research analysis
- No complex NLP processing (kept simple for speed)

### **🌐 discover-web-sources**
- Web source discovery and cataloging
- Domain authority and credibility scoring
- Source monitoring for ongoing data collection

### **📋 tavily://api-info**
- Resource with integration details and capabilities
- Best practices for geospatial intelligence
- Rate limiting and performance guidance

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Tavily API key (get one at [tavily.com](https://tavily.com))

### Installation
```bash
# Install dependencies
pip install fastmcp>=2.11.4 httpx python-dotenv pydantic

# Set up environment
echo "TAVILY_API_KEY=your_tavily_api_key_here" >> .env

## Future Enhancements
 Azure Service Bus integration - For orchestrated workflows
 Redis caching - Country-level result caching
 Advanced location filtering - Coordinate-based scoping
 Source monitoring - Automated source quality tracking