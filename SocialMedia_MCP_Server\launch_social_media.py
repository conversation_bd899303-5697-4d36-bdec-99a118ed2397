#!/usr/bin/env python3
"""
Launcher script for the Geospatial Social Media MCP Server
"""
import os
import sys
from dotenv import load_dotenv

def main():
    """Launch the Social Media MCP Server"""
    load_dotenv()
    
    if not os.getenv("APIFY_API_TOKEN"):
        print("Error: APIFY_API_TOKEN environment variable is not set")
        print("Please add your Apify API token to the .env file:")
        print("APIFY_API_TOKEN=your_api_token_here")
        print("Get your token at: https://console.apify.com/account/integrations")
        sys.exit(1)
    
    print("Starting Geospatial Social Media MCP Server...")
    print(f"API Token configured: {os.getenv('APIFY_API_TOKEN')[:8]}...")
    print("Platforms: Instagram, YouTube")
    
    try:
        from social_media_server import mcp
        print("Server starting with Apify actor integration...")
        mcp.run()
    except ImportError as e:
        print(f"Import error: {e}")
        print("Install dependencies: pip install fastmcp httpx python-dotenv pydantic")
        sys.exit(1)
    except Exception as e:
        print(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()