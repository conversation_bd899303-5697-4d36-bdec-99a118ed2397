# social_media_server.py
import os
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field

import httpx
from fastmcp import FastMCP
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP("Geospatial Social Media MCP Server")

# Apify API configuration
APIFY_API_TOKEN = os.getenv("APIFY_API_TOKEN")
APIFY_API_BASE_URL = "https://api.apify.com/v2"

if not APIFY_API_TOKEN:
    raise ValueError("APIFY_API_TOKEN environment variable is required")

# Platform-specific actor configurations
ACTORS = {
    "instagram": {
        "actor_id": "apify/instagram-scraper",
        "url": "https://apify.com/apify/instagram-scraper"
    },
    "youtube": {
        "actor_id": "streamers/youtube-scraper", 
        "url": "https://apify.com/streamers/youtube-scraper"
    }
}

# Pydantic models for type safety and validation
class InstagramSearchRequest(BaseModel):
    """Request model for Instagram scraping via Apify"""
    search_terms: List[str] = Field(description="Search terms for Instagram content")
    search_type: Literal["user", "hashtag", "place"] = Field("hashtag", description="Type of search to perform")
    time_window: Optional[str] = Field("7d", description="Time window for content (e.g., '7d', '1m')")
    result_limit: int = Field(20, ge=1, le=100, description="Maximum number of results to return")
    include_comments: bool = Field(False, description="Whether to include comments in scraping")

class YouTubeSearchRequest(BaseModel):
    """Request model for YouTube scraping via Apify"""
    search_queries: List[str] = Field(description="Search queries for YouTube content")
    start_urls: Optional[List[str]] = Field(None, description="Specific YouTube URLs to scrape")
    max_results: int = Field(20, ge=1, le=100, description="Maximum number of video results")
    include_comments: bool = Field(False, description="Whether to include video comments")
    include_subtitles: bool = Field(False, description="Whether to include video subtitles")

class SocialMediaMonitoringRequest(BaseModel):
    """Request model for ongoing social media monitoring"""
    platforms: List[Literal["instagram", "youtube"]] = Field(description="Platforms to monitor")
    keywords: List[str] = Field(description="Keywords to monitor across platforms")
    location_filter: Optional[str] = Field(None, description="Location-based filtering")
    refresh_interval: int = Field(5, ge=2, le=10, description="Refresh interval in minutes (2-10min)")

# HTTP client for Apify API requests
async def make_apify_request(actor_id: str, input_data: dict, timeout: int = 300) -> dict:
    """Make authenticated request to Apify API"""
    headers = {
        "Authorization": f"Bearer {APIFY_API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Start actor run
    start_url = f"{APIFY_API_BASE_URL}/acts/{actor_id}/runs"
    
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            # Start the actor
            start_response = await client.post(start_url, json=input_data, headers=headers)
            start_response.raise_for_status()
            run_data = start_response.json()
            
            run_id = run_data["data"]["id"]
            
            # Wait for completion and get results
            results_url = f"{APIFY_API_BASE_URL}/acts/{actor_id}/runs/{run_id}/dataset/items"
            
            # Poll for completion (simplified - in production use webhooks)
            import asyncio
            max_wait = 120  # 2 minutes max wait
            wait_time = 0
            
            while wait_time < max_wait:
                await asyncio.sleep(5)
                wait_time += 5
                
                # Check if run completed
                status_url = f"{APIFY_API_BASE_URL}/acts/{actor_id}/runs/{run_id}"
                status_response = await client.get(status_url, headers=headers)
                status_data = status_response.json()
                
                if status_data["data"]["status"] in ["SUCCEEDED", "FAILED"]:
                    break
            
            # Get results
            results_response = await client.get(results_url, headers=headers)
            results_response.raise_for_status()
            return results_response.json()
            
        except httpx.HTTPError as e:
            logger.error(f"Apify API request failed: {e}")
            raise Exception(f"Apify API error: {str(e)}")

def format_instagram_results(data: List[dict], search_terms: List[str]) -> str:
    """Format Instagram scraping results for Deep Research MCP consumption"""
    if not data:
        return f"No Instagram results found for search terms: {', '.join(search_terms)}"
    
    response = f" Instagram Results for '{', '.join(search_terms)}' (Found {len(data)} items):\n\n"
    
    for i, item in enumerate(data, 1):
        response += f"Instagram Post {i}:\n"
        response += f"Type: {item.get('type', 'post')}\n"
        response += f"Caption: {item.get('caption', 'No caption')[:200]}...\n"
        response += f"Owner: @{item.get('ownerUsername', 'unknown')}\n"
        response += f"Likes: {item.get('likesCount', 0):,}\n"
        response += f"Comments: {item.get('commentsCount', 0):,}\n"
        response += f"Display URL: {item.get('displayUrl', 'N/A')}\n"
        
        # Add location if available
        if item.get('locationName'):
            response += f"Location: {item.get('locationName')}\n"
        
        # Add timestamp if available
        if item.get('timestamp'):
            response += f"Posted: {item.get('timestamp')}\n"
        
        response += "─" * 50 + "\n"
    
    return response

def format_youtube_results(data: List[dict], search_queries: List[str]) -> str:
    """Format YouTube scraping results for Deep Research MCP consumption"""
    if not data:
        return f"No YouTube results found for queries: {', '.join(search_queries)}"
    
    response = f" YouTube Results for '{', '.join(search_queries)}' (Found {len(data)} videos):\n\n"
    
    for i, video in enumerate(data, 1):
        response += f"YouTube Video {i}:\n"
        response += f"Title: {video.get('title', 'No title')}\n"
        response += f"Channel: {video.get('channel_info', {}).get('name', 'Unknown channel')}\n"
        response += f"Views: {video.get('view_count', 0):,}\n"
        response += f"Likes: {video.get('like_count', 0):,}\n"
        response += f"Duration: {video.get('duration', 'Unknown')}\n"
        response += f"Description: {video.get('description', 'No description')[:200]}...\n"
        response += f"URL: {video.get('url', 'N/A')}\n"
        
        # Add upload date if available
        if video.get('upload_date'):
            response += f"Uploaded: {video.get('upload_date')}\n"
        
        response += "─" * 50 + "\n"
    
    return response

def format_monitoring_results(results: Dict[str, List[dict]], keywords: List[str]) -> str:
    """Format cross-platform monitoring results"""
    response = f"Social Media Monitoring Results for keywords: {', '.join(keywords)}\n\n"
    
    total_items = sum(len(items) for items in results.values())
    response += f"Total items found: {total_items}\n\n"
    
    for platform, items in results.items():
        if items:
            response += f"{platform.title()} ({len(items)} items):\n"
            for item in items[:3]:  # Show first 3 items per platform
                if platform == "instagram":
                    response += f"  • @{item.get('ownerUsername', 'unknown')}: {item.get('caption', 'No caption')[:100]}...\n"
                elif platform == "youtube":
                    response += f"  • {item.get('title', 'No title')[:100]}... (Views: {item.get('view_count', 0):,})\n"
            
            if len(items) > 3:
                response += f"  ... and {len(items) - 3} more items\n"
            response += "\n"
    
    return response

# MCP Tools using FastMCP decorators (hyphenated naming convention)
@mcp.tool()
async def scrape_instagram_content(request: InstagramSearchRequest) -> str:
    """
    Scrape Instagram content using Apify instagram-scraper.
    Returns scraped_items[] with posts, profiles, hashtags, or places data.
    Refresh rate: 2-10min for real-time social media intelligence.
    """
    # Prepare Apify input for Instagram scraper
    input_data = {
        "searchTerms": request.search_terms,
        "searchType": request.search_type,
        "resultsLimit": request.result_limit,
        "includeComments": request.include_comments
    }
    
    # Add time window if specified
    if request.time_window:
        input_data["timeWindow"] = request.time_window
    
    try:
        data = await make_apify_request(ACTORS["instagram"]["actor_id"], input_data)
        return format_instagram_results(data, request.search_terms)
        
    except Exception as e:
        logger.error(f"Instagram scraping failed: {e}")
        return f"Instagram scraping error: {str(e)}"

@mcp.tool()
async def scrape_youtube_content(request: YouTubeSearchRequest) -> str:
    """
    Scrape YouTube content using Apify youtube-scraper.
    Returns video_items[] with title, view_count, like_count, duration, channel_info, description.
    Refresh rate: 2-10min with optional comments and subtitles.
    """
    # Prepare Apify input for YouTube scraper
    input_data = {
        "searchQueries": request.search_queries,
        "maxResults": request.max_results,
        "includeComments": request.include_comments,
        "includeSubtitles": request.include_subtitles
    }
    
    # Add specific URLs if provided
    if request.start_urls:
        input_data["startUrls"] = [{"url": url} for url in request.start_urls]
    
    try:
        data = await make_apify_request(ACTORS["youtube"]["actor_id"], input_data)
        return format_youtube_results(data, request.search_queries)
        
    except Exception as e:
        logger.error(f"YouTube scraping failed: {e}")
        return f"YouTube scraping error: {str(e)}"

@mcp.tool()
async def monitor_social_platforms(request: SocialMediaMonitoringRequest) -> str:
    """
    Monitor multiple social media platforms for keyword mentions.
    Provides cross-platform social media intelligence with adaptive pacing.
    Implements 2-10min refresh rates to balance discovery vs. detection risk.
    """
    results = {}
    
    for platform in request.platforms:
        try:
            if platform == "instagram":
                # Create Instagram request
                instagram_request = InstagramSearchRequest(
                    search_terms=request.keywords,
                    search_type="hashtag",
                    result_limit=10
                )
                instagram_data = await make_apify_request(
                    ACTORS["instagram"]["actor_id"], 
                    {
                        "searchTerms": instagram_request.search_terms,
                        "searchType": instagram_request.search_type,
                        "resultsLimit": instagram_request.result_limit
                    }
                )
                results[platform] = instagram_data
                
            elif platform == "youtube":
                # Create YouTube request
                youtube_request = YouTubeSearchRequest(
                    search_queries=request.keywords,
                    max_results=10
                )
                youtube_data = await make_apify_request(
                    ACTORS["youtube"]["actor_id"],
                    {
                        "searchQueries": youtube_request.search_queries,
                        "maxResults": youtube_request.max_results
                    }
                )
                results[platform] = youtube_data
                
        except Exception as e:
            logger.error(f"Failed to monitor {platform}: {e}")
            results[platform] = []
    
    return format_monitoring_results(results, request.keywords)

# Resource for API information
@mcp.resource("apify://social-media-info")
async def get_social_media_info() -> str:
    """
    Information about the Apify social media integration and scraping capabilities.
    """
    return """
# Social Media MCP Server - Apify Integration

## Current Configuration
- API Endpoint: https://api.apify.com/v2
- Authentication: API Token configured
- Supported Platforms: Instagram, YouTube
- Refresh Rates: 2-10min (adaptive pacing for anti-scraping)

## Platform Specifications

### Instagram (apify/instagram-scraper)
- **Inputs**: search_terms[], search_type (user, hashtag, place), time_window, result_limit
- **Outputs**: scraped_items[] with caption, likesCount, commentsCount, displayUrl, ownerUsername, locationName
- **Refresh**: 2-10min (balances discovery vs. detection risk)
- **Anti-scraping**: Platform detects frequent scraping - variable window mitigates risk

### YouTube (streamers/youtube-scraper)  
- **Inputs**: search_queries[], start_urls[], max_results
- **Outputs**: video_items[] with title, view_count, like_count, duration, channel_info, description
- **Refresh**: 2-10min (comments drive urgency despite less frequent video updates)
- **Options**: Optional comments[] and subtitles[] extraction

## Scraping Resilience Strategy

### Fallback Tiering (per Section 3.2)
1. **Tier 1**: Apify actor (primary)
2. **Tier 2**: Official API with tenant-owned credentials
3. **Tier 3**: RSS/public mirrors or web archives

### Anti-Block Strategies
- Rotating IPs through Apify infrastructure
- Adaptive pacing (2-10min variable windows)
- Randomized headers and request patterns
- Respect robots.txt unless legal authorization

### Content Safety Gates
- Jurisdictional review for sensitive regions
- Content filtering for policy compliance
- Rate limiting to respect platform terms

## Performance Targets
- **Latency**: Variable due to Apify actor execution (30s+ expected)
- **Async Processing**: Required for actors >30s wall-clock (per Section 9.5)
- **Timeout**: 15min ceiling for actor runs
- **Concurrency**: Parallel execution across platforms

## Integration Notes
- Feeds raw social media data to Deep Research MCP
- No sentiment analysis (handled by Deep Research MCP)
- Location-aware filtering supported through search terms
- Geospatial intelligence through hashtag/place targeting

## Rate Limit Management
- Monitor Apify usage and quotas
- Implement circuit breakers for actor failures
- Queue management for high-volume requests
- Consider premium Apify plans for production scale

## Best Practices for Geospatial Intelligence
- Use location-specific hashtags for hyperlocal content
- Combine platform searches for comprehensive coverage
- Monitor trending topics for emerging intelligence
- Filter by time windows for real-time awareness
"""

if __name__ == "__main__":
    # Run the MCP server with STDIO transport for Claude Desktop integration
    # For HTTP testing, you can change to: mcp.run(transport="http", port=8002, host="localhost")
    mcp.run()