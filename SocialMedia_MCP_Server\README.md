# Geospatial Social Media MCP Server

A comprehensive Model Context Protocol (MCP) server for multi-platform social media intelligence gathering, built with **FastMCP** for the Geospatial project. Provides real-time social media monitoring and content extraction using Apify actors across 11 major platforms.

## 🌟 Overview

The Social Media MCP Server is a critical component of the Geospatial intelligence platform, delivering:

- **Multi-platform coverage**: 11 social media platforms with specialized scraping
- **Adaptive refresh rates**: Platform-optimized timing (2-20min) to balance discovery vs. detection risk
- **Hyperlocal intelligence**: Location-aware filtering for geospatial analysis
- **Raw content delivery**: Feeds structured data to Deep Research MCP for sentiment analysis
- **Anti-scraping resilience**: Fallback strategies and adaptive pacing

## 🚀 Supported Platforms

### **High-Velocity Platforms (2-10min refresh)**
| Platform | Actor | Inputs | Outputs | Rationale |
|----------|-------|--------|---------|-----------|
| **Instagram** | `apify/instagram-scraper` | search_terms[], search_type, time_window, result_limit | scraped_items[] (caption, likesCount, commentsCount, displayUrl, ownerUsername, locationName) | Visual content spreads rapidly; adaptive timing balances discovery vs. detection |
| **YouTube** | `streamers/youtube-scraper` | search_queries[], start_urls[], max_results | video_items[] (title, view_count, like_count, duration, channel_info, description, comments[], subtitles[]) | Video updates less frequent but comments drive urgency |
| **X (Twitter)** | `danek/twitter-timeline` | username, max_posts | tweets[] (tweet_id, text, created_at, favorites, retweets, replies, quotes, views, author, media[]) | Real-time nature demands frequent checks despite API limits |

### **Medium-Velocity Platforms (5-15min refresh)**
| Platform | Actor | Inputs | Outputs | Rationale |
|----------|-------|--------|---------|-----------|
| **TikTok** | `clockworks/tiktok-scraper` | hashtags[], resultsPerPage | videos[] (text, authorMeta.name, createTimeISO, diggCount, shareCount, playCount, commentCount, collectCount, videoMeta.duration) | Dynamic content with moderate anti-scraping measures |
| **Bluesky** | `lexis-solutions/bluesky-posts-scraper` | queries[], limit, sort | posts[] (id, text, createdAt, authorUsername, authorName, images[], replyCount, repostCount, likeCount) | Emerging platform with balanced content velocity |

### **Low-Velocity Platforms (10-20min refresh)**
| Platform | Actor | Inputs | Outputs | Rationale |
|----------|-------|--------|---------|-----------|
| **LinkedIn** | `curious_coder/linkedin-post-search-scraper` | urls[], deepScrape | posts[] (text, author, postedAtISO, numLikes, numComments, numShares, comments[], reactions[]) | Professional network with less frequent but high-value updates |
| **Facebook** | `apify/facebook-posts-scraper` | startUrls[], resultsLimit, maxRequestRetries | posts[] (text, time, timestamp, likes, comments, shares, link, thumb, pageName, url) | Strict anti-scraping measures require longer intervals |
| **Reddit** | `trudax/reddit-scraper-lite` | startUrls[], searches[], sort, maxItems | items[] (title, body, username, communityName, upVotes, numberOfComments, createdAt) | Community platform with longer discussion cycles |
| **Truth Social** | `muhammetakkurtt/truth-social-scraper` | username, maxPosts, onlyReplies, onlyMedia | posts[] (id, content, created_at, replies_count, reblogs_count, favourites_count, account, media_attachments[]) | Platform-specific constraints require conservative approach |
| **Discord** | `curious_coder/discord-data-scraper` | action, channelUrl, minDelay, maxDelay | messages[] or members[] (content, author, timestamp, attachments[], mentions[], message_reference) | Server-based platform with rate limiting considerations |

### **Critical Communication (2-5min refresh)**
| Platform | Actor | Inputs | Outputs | Rationale |
|----------|-------|--------|---------|-----------|
| **Telegram** | `tri_angle/telegram-scraper` | profiles[], scrapeLastNDays, oldestMessageDate | messages[] (username, fullName, bio, fulldate, views, description, preview.link, link) | Real-time crisis communication in sensitive regions |

## ✨ Key Features

### **🔧 MCP Tools (Hyphenated Naming)**
- **`scrape-instagram-content`**: Instagram posts, profiles, hashtags, places
- **`scrape-youtube-content`**: Video content with optional comments/subtitles  
- **`scrape-twitter-timeline`**: Tweet extraction with engagement metrics
- **`scrape-linkedin-posts`**: Professional network content monitoring
- **`scrape-tiktok-videos`**: Short-form video intelligence
- **`scrape-telegram-channels`**: Real-time messaging intelligence
- **`monitor-social-platforms`**: Cross-platform keyword monitoring
- **`extract-location-content`**: Geospatial-filtered social intelligence

### **📊 Resources**
- **`apify://social-media-info`**: Platform specifications and capabilities
- **`apify://scraping-resilience`**: Anti-blocking strategies and fallbacks

## 🏗 Architecture Integration

### **Data Flow**
Social Media Platforms → Apify Actors → Social Media MCP → Deep Research MCP → Intelligence Reports

### **Role in Geospatial Ecosystem**
- ✅ **Raw content provider**: No sentiment analysis (handled by Deep Research MCP)
- ✅ **Location-aware filtering**: Hashtag/place targeting for hyperlocal intelligence  
- ✅ **Multi-source ingestion**: Feeds parallel data streams to Deep Research
- ✅ **Real-time monitoring**: Adaptive refresh rates for time-sensitive intelligence

## 🚀 Quick Start

### **Prerequisites**
- Python 3.11+
- Apify account and API token
- FastMCP 2.x framework

### **Installation**
```bash
# Install dependencies
pip install fastmcp>=2.11.4 httpx python-dotenv pydantic

# Configure environment
echo "APIFY_API_TOKEN=your_apify_token_here" >> .env
Configuration
json
// fastmcp.json
{
  "entrypoint": "social_media_server.py",
  "environment": {
    "dependencies": ["fastmcp>=2.11.4", "httpx", "python-dotenv", "pydantic"]
  },
  "server": {
    "name": "Geospatial Social Media MCP Server",
    "version": "1.0.0",
    "description": "Multi-platform social media intelligence using Apify actors"
  }
}
Running the Server
bash
# Option 1: Direct run
python social_media_server.py

# Option 2: Using launcher
python launch_social_media.py
📋 Usage Examples
Instagram Content Scraping
json
{
  "search_terms": ["#cybersecurity", "#datasecurity"],
  "search_type": "hashtag",
  "time_window": "7d",
  "result_limit": 50
}
YouTube Intelligence Gathering
json
{
  "search_queries": ["geopolitical analysis", "threat intelligence"],
  "max_results": 25,
  "include_comments": true,
  "include_subtitles": false
}
Cross-Platform Monitoring
json
{
  "platforms": ["instagram", "twitter", "telegram"],
  "keywords": ["supply chain", "infrastructure", "security"],
  "location_filter": "Eastern Europe",
  "refresh_interval": 5
}
Hyperlocal Intelligence
json
{
  "location_scope": "Kandahar Province",
  "platforms": ["instagram", "telegram"],
  "search_terms": ["#Kandahar", "security situation"],
  "time_window": "24h"
}
🛡 Scraping Resilience Strategy
Fallback Tiering
Tier 1: Apify actor (primary method)
Tier 2: Official API with tenant-owned credentials
Tier 3: RSS/public mirrors or web archives
Anti-Block Measures
✅ Rotating IPs: Through Apify infrastructure
✅ Adaptive pacing: Variable 2-20min windows per platform
✅ Randomized headers: Request pattern obfuscation
✅ Circuit breakers: Prevent cascading failures
✅ Rate limiting: Respect platform terms of service
Content Safety
✅ Jurisdictional review: Sensitive region compliance
✅ robots.txt compliance: Unless legal authorization exists
✅ Content filtering: Policy compliance validation
⚡ Performance Specifications
Latency Expectations
Apify Actor Execution: 30s+ (async processing required)
Timeout Ceiling: 15min maximum per actor run
Async Processing: Required for actors >30s wall-clock time
Concurrency: Parallel execution across platforms
Refresh Rate Optimization
Platform Type	Refresh Rate	Optimization Strategy
High-Velocity	2-10min	Adaptive pacing to avoid detection
Medium-Velocity	5-15min	Burst processing during high-activity
Low-Velocity	10-20min	Conservative timing for strict platforms
Critical Comm	2-5min	Optimized for crisis communication
🔗 Integration Points
Deep Research MCP Integration
✅ Raw content delivery: Structured data without sentiment analysis
✅ Location metadata: Geographic context for hyperlocal analysis
✅ Engagement metrics: Social signals for credibility assessment
✅ Temporal context: Time-based filtering for trend analysis
Caching Strategy
social:feed:{tenant}:{platform}:{location} (TTL: 2-5min)
Use case: Live dashboard widgets for location-specific social media
Reduces API calls while maintaining freshness for real-time monitoring
📊 Monitoring & Observability
Key Metrics
Actor Success Rate: >95% completion rate per platform
Refresh Compliance: Adherence to platform-specific timing windows
Data Freshness: <10min for high-velocity platforms (per SLI requirements)
Queue Depth: <1000 messages for platform processing
Alerting Thresholds
Actor Failures: >5% failure rate over 10min window
Rate Limiting: Platform-specific quota exhaustion warnings
Data Quality: Missing location/engagement metadata alerts
Latency Breaches: Actors exceeding 15min timeout ceiling
🔮 Future Enhancements
Phase 2 Capabilities
 Azure Service Bus integration for orchestrated workflows
 Redis caching layer for improved performance
 Custom polygon support for irregular geographic boundaries
 Real-time WebSocket streams for critical platform monitoring
 ML-based content filtering for enhanced data quality
Additional Platforms
 Mastodon support for decentralized social networks
 WeChat integration for Asian market coverage
 Clubhouse audio content intelligence
 Snapchat ephemeral content monitoring
🏷 Tags & Categories
Primary: social-media, intelligence-gathering, multi-platform, geospatial, fastmcp
Secondary: apify, scraping, real-time, hyperlocal, content-extraction
Technical: python, async, rate-limiting, anti-detection, azure-integration
Built with FastMCP 2.x | Optimized for Geospatial Intelligence | Production-Ready Architecture

This comprehensive README provides:

1. **Complete platform coverage** with all 11 supported social media platforms
2. **Detailed refresh rate rationale** explaining the timing strategies
3. **Architecture integration** showing how it fits into the larger Geospatial ecosystem  
4. **Practical usage examples** for different intelligence gathering scenarios
5. **Performance specifications** aligned with the documented SLI/SLO requirements
6. **Scraping resilience strategy** following the three-tier fallback approach
7. **Future enhancement roadmap** for planned Azure integration capabilities

The README follows the same structure and quality as the NewsAPI server documentation while highlighting the unique aspects of social media intelligence gathering and the sophisticated anti-scraping measures required for these platforms.