#!/usr/bin/env python3
"""
Launcher script for the Geospatial Internet Search MCP Server
"""
import os
import sys
from dotenv import load_dotenv

def main():
    """Launch the Internet Search MCP Server"""
    load_dotenv()
    
    if not os.getenv("TAVILY_API_KEY"):
        print(" Error: TAVILY_API_KEY environment variable is not set")
        print("Please add your Tavily API key to the .env file:")
        print("TAVILY_API_KEY=your_api_key_here")
        sys.exit(1)
    
    print(" Starting Geospatial Internet Search MCP Server...")
    print(f" API Key configured: {os.getenv('TAVILY_API_KEY')[:8]}...")
    
    try:
        from internet_search_server import mcp
        print(" Server starting with Tavily integration...")
        mcp.run()
    except ImportError as e:
        print(f" Import error: {e}")
        print("Install dependencies: pip install fastmcp httpx python-dotenv pydantic")
        sys.exit(1)
    except Exception as e:
        print(f" Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()