# test_news_server.py
import asyncio
import os
from unittest.mock import patch, AsyncMock, MagicMock
import pytest

# Setup test environment before imports
def setup_test_env():
    """Setup test environment before imports"""
    if not os.getenv("NEWSAPI_APIKEY"):
        os.environ["NEWSAPI_APIKEY"] = "test_api_key_for_testing"

setup_test_env()

# Import the underlying functions and models, not the wrapped tools
from news_server import NewsSearchRequest, HeadlinesRequest, make_news_request, format_articles_response

# Mock response data
MOCK_NEWS_RESPONSE = {
    "status": "ok",
    "totalResults": 2,
    "articles": [
        {
            "title": "Test Article 1",
            "source": {"name": "Test Source"},
            "author": "Test Author",
            "publishedAt": "2025-01-15T10:00:00Z",
            "description": "Test description 1",
            "url": "https://example.com/article1"
        },
        {
            "title": "Test Article 2", 
            "source": {"name": "Another Source"},
            "author": "Another Author",
            "publishedAt": "2025-01-15T11:00:00Z",
            "description": "Test description 2",
            "url": "https://example.com/article2"
        }
    ]
}

@pytest.mark.asyncio
async def test_news_api_request():
    """Test the make_news_request function"""
    # Mock the HTTP request
    with patch('news_server.httpx.AsyncClient') as mock_client:
        # Create mock response
        mock_response = MagicMock()
        mock_response.json.return_value = MOCK_NEWS_RESPONSE
        mock_response.raise_for_status = MagicMock()
        
        # Setup the async context manager and client behavior
        mock_client_instance = AsyncMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        result = await make_news_request("everything", {"q": "test"})
        
        assert result["status"] == "ok"
        assert len(result["articles"]) == 2
        assert result["articles"][0]["title"] == "Test Article 1"

@pytest.mark.asyncio
async def test_format_articles_response():
    """Test the format_articles_response function"""
    result = format_articles_response(MOCK_NEWS_RESPONSE, "test search")
    
    # Verify the response contains expected content
    assert "Test Search Results" in result
    assert "Test Article 1" in result
    assert "Test Article 2" in result
    assert "Found 2 articles" in result

@pytest.mark.asyncio
async def test_pydantic_models():
    """Test that Pydantic models work correctly"""
    # Test NewsSearchRequest
    search_request = NewsSearchRequest(
        query="artificial intelligence",
        language="en",
        page_size=5
    )
    assert search_request.query == "artificial intelligence"
    assert search_request.language == "en"
    assert search_request.page_size == 5
    
    # Test HeadlinesRequest
    headlines_request = HeadlinesRequest(
        country="us",
        category="technology",
        page_size=5
    )
    assert headlines_request.country == "us"
    assert headlines_request.category == "technology"
    assert headlines_request.page_size == 5

@pytest.mark.asyncio
async def test_live_api():
    """Test with real API (requires valid API key)"""
    if not os.getenv("NEWSAPI_APIKEY") or os.getenv("NEWSAPI_APIKEY") == "test_api_key_for_testing":
        pytest.skip("Skipping live API test - no valid API key found")
        return
    
    try:
        # Test the underlying make_news_request function
        params = {
            "q": "technology",
            "pageSize": 3,
            "language": "en"
        }
        
        result = await make_news_request("everything", params)
        
        # Basic validation of real API response
        assert "status" in result
        assert result["status"] == "ok" or "error" in result.get("message", "")
        
        if result["status"] == "ok":
            assert "articles" in result
            print("\n Live API Test successful!")
            print(f"Found {result.get('totalResults', 0)} articles")
        else:
            print(f"\n API returned error: {result.get('message', 'Unknown error')}")
        
    except Exception as e:
        print(f"\n Live API test failed: {e}")
        # Don't fail the test for network issues
        pytest.skip(f"Live API test skipped due to: {e}")

if __name__ == "__main__":
    print(" Running FastMCP NewsAPI Server Tests...")
    
    # Configure pytest for async support
    import sys
    sys.exit(pytest.main([
        __file__, 
        "-v", 
        "--asyncio-mode=auto",
        "--tb=short"
    ]))