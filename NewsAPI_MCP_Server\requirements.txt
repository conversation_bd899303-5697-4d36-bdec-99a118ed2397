# Core FastMCP and MCP dependencies
fastmcp>=2.11.4
mcp>=1.12.4

# HTTP client for API requests
httpx>=0.25.0

# Environment and configuration
python-dotenv>=1.0.0

# Data validation and type safety
pydantic>=2.11.7
pydantic[email]>=2.11.7

# Testing dependencies
pytest>=7.0.0
pytest-asyncio>=0.20.0

# Additional FastMCP dependencies
rich>=13.9.4
uvicorn>=0.31.1
starlette>=0.27
anyio>=4.5
jsonschema>=4.20.0

# Authentication and security
authlib>=1.5.2

# CLI and utilities
cyclopts>=3.0.0
exceptiongroup>=1.2.2

# OpenAPI support
openapi-core>=0.19.5
openapi-pydantic>=0.5.1

# Additional utilities
typing-extensions>=4.12.2
requests>=2.31.0