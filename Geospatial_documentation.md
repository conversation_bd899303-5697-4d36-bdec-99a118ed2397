# Project Geospatial - Streamlined Technical Design

## 1. System Overview

Project Geospatial is an Azure-native intelligence platform providing multi-source parallel ingestion, deep research analysis, internet trend discovery, and automated reporting with confidence scoring.

### Core Workflow

User Request → Upstream AI Agent (Location Validation) → API Gateway → Service Bus Fan-out → Parallel MCPs → Deep Research Processing → Validation AI Agent → Report Generation → Upstream AI Agent (Delivery) → Final Delivery Channels

### Key Capabilities

- Multi-source ingestion: News, Social Media, Web Search, Internet Trends
- Hyperlocal intelligence: Country to neighborhood granularity
- Real-time processing: Live feeds with confidence scoring
- Automated reporting: PDF reports and interactive dashboards
- Smart alerting: Rule-based notifications with severity mapping

## 2. Architecture Components

### 2.1 Control Plane

- Azure App Service (FastAPI, containerized)
- JWT authentication with Azure AD B2C
- Service Bus orchestration for MCP fan-out with direct routing to Deep Research
- Auto-scaling: 2-10 instances based on CPU/queue metrics

### 2.2 MCP Ecosystem

| MCP             | Provider           | Input                                        | Output                                                                  | Refresh         |
| --------------- | ------------------ | -------------------------------------------- | ----------------------------------------------------------------------- | --------------- |
| News            | NewsAPI            | location, filters, window                    | articles[]                                                              | 2-5min          |
| Social          | Refer table 3.1    | Refer table 3.1                              | Refer table 3.1                                                         | Refer table 3.1 |
| Internet Search | Tavily             | keywords[], location_scope                   | raw_documents[], topics[]                                               | Real-Time       |
| Deep Research   | LangChain + Tavily | raw_documents[], location, context           | enriched_docs[], findings[], summaries, clusters[], confidence_scores[] | Real-time       |
| Reporting       | Custom             | sections[], location, theme, confidence_data | pdf_uri, dashboard_json                                                 | 3-15s           |
| Email           | SendGrid           | breaking_developments[], ruleset_id          | alerts[], delivery_status                                               | Real-time       |
| Validation AI   | Custom             | report_id, raw_findings                      | validation_results[], quality_score                                     | Real-time       |

**Notes:**

- Deep Research MCP serves as the central intelligence processing engine for all analysis
- Internet Search MCP provides raw web search results only; Deep Research MCP performs all analysis
- LangChain + Tavily is exclusive to Deep Research MCP for content processing and fact-checking
- Internet Search MCP uses Tavily exclusively for basic web search and trend identification (no sentiment analysis)
- Email MCP generates alerts based on validated Deep Research findings
- Validation AI Agent performs final quality check before report delivery
- Upstream AI Agent validates location at workflow initiation and handles final delivery

#### **Rationale behind refresh rates:**

### 1. News (<5min refresh, typically 2-5min)

**Rationale:**

* NewsAPI provides time-sensitive information where minutes matter for actionable intelligence
* Major news outlets typically update stories every 3-5 minutes during breaking events
* Shorter intervals would exceed NewsAPI rate limits and increase costs unnecessarily
* Longer intervals would miss critical developments in fast-moving situations
* The 2-5 minute window balances freshness with API constraints and processing overhead

### 2.2.1 NewsAPI Endpoint Selection Logic

| Criterion                               | Endpoint (Links)                         | Purpose                                               | Example Call Parameters                                              |
| --------------------------------------- | ---------------------------------------- | ----------------------------------------------------- | -------------------------------------------------------------------- |
| Real-time, location-centric headlines   | GET https://newsapi.org/v2/top-headlines | Fast, trending stories for a country, region, or city | country=us or category=business                                      |
| Keyword, historical, or filtered search | GET https://newsapi.org/v2/everything    | Deep-dive research, long-tail sources, date windows   | q= "supply chain " &from=2025-08-01 &to=2025-09-15 &sortBy=relevancy |

### 2.2.2 Apify Actors Links for Social Media MCP Servers

| Platform               | Actor owner & name                         | Direct actor run URL                                         |
| ---------------------- | ------------------------------------------ | ------------------------------------------------------------ |
| Instagram              | apify/instagram-scraper                    | https://apify.com/apify/instagram-scraper                    |
| TikTok                 | clockworks/tiktok-scraper                  | https://apify.com/clockworks/tiktok-scraper                  |
| Facebook Posts Scraper | apify/facebook-posts-scraper               | https://apify.com/apify/facebook-posts-scraper               |
| X (Twitter)            | danek/twitter-timeline                     | https://apify.com/danek/twitter-timeline                     |
| LinkedIn Post Scraper  | curious_coder/linkedin-post-search-scraper | https://apify.com/curious_coder/linkedin-post-search-scraper |
| YouTube                | streamers/youtube-scraper                  | https://apify.com/streamers/youtube-scraper                  |
| Truth Social           | muhammetakkurtt/truth-social-scraper       | https://apify.com/muhammetakkurtt/truth-social-scraper       |
| Bluesky                | lexis-solutions/bluesky-posts-scraper      | https://apify.com/lexis-solutions/bluesky-posts-scraper      |
| Reddit                 | trudax/reddit-scraper-lite                 | https://apify.com/trudax/reddit-scraper-lite                 |
| Discord                | curious_coder/discord-data-scraper         | https://apify.com/curious_coder/discord-data-scraper         |
| Telegram               | tri_angle/telegram-scraper                 | https://apify.com/tri_angle/telegram-scraper                 |

### 2.3 Data Layer

- PostgreSQL: System of record with tenant isolation (RLS)
- Blob Storage: Raw content, media, reports
- Redis: Hot cache with location-scoped keys
- Web PubSub: Real-time notifications

## 3. Social Media Platform Coverage

### 3.1 Supported Platforms

| Platform     | Provider                 | Inputs                                                                       | Outputs                                                                                                                                                          | Refresh  |
| ------------ | ------------------------ | ---------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| Instagram    | Apify actors             | search_term[], search_type (user, hashtag, place), time_window, result_limit | scraped_items[] (type: post, profile, hashtag, or place) — each includes fields like caption, likesCount, commentsCount, displayUrl, ownerUsername, locationName | 2-10min  |
| YouTube      | Apify / YouTube Data API | search_queries[], start_urls[], max_results                                  | video_items[] (incl. title, view_count, like_count, duration, channel_info, description), optional comments[], optional subtitles[]                              | 2-10min  |
| X (Twitter)  | Apify / X API            | username, max_posts                                                          | tweets[] (incl. tweet_id, text, created_at, favorites, retweets, replies, quotes, views, author, media[])                                                        | 2-10min  |
| LinkedIn     | Apify Actors             | urls[], deepScrape                                                           | posts[] (incl. text, author, postedAtISO, numLikes, numComments, numShares, comments[], reactions[])                                                             | 10-20min |
| TikTok       | Apify Actors             | hashtags[], resultsPerPage                                                   | videos[] (incl. text, authorMeta.name, createTimeISO, diggCount, shareCount, playCount, commentCount, collectCount, videoMeta.duration)                          | 5-15min  |
| Truth Social | Apify Actors             | username, maxPosts, onlyReplies, onlyMedia                                   | posts[] (incl. id, content, created_at, replies_count, reblogs_count, favourites_count, account, media_attachments[])                                            | 10-20min |
| Bluesky      | Apify Actors             | queries[], limit, sort                                                       | posts[] (incl. id, text, createdAt, authorUsername, authorName, images[], replyCount, repostCount, likeCount)                                                    | 5-15min  |
| Facebook     | Apify actors             | startUrls[], resultsLimit, maxRequestRetries                                 | posts[] (incl. text, time, timestamp, likes, comments, shares, link, thumb, pageName, url)                                                                       | 10-20min |
| Reddit       | Apify actors             | startUrls[], searches[], sort, maxItems                                      | items[] (type: post, comment, community, or user) — each includes fields like title, body, username, communityName, upVotes, numberOfComments, createdAt)        | 10-20min |
| Telegram     | Apify Actors             | profiles[], scrapeLastNDays, oldestMessageDate                               | messages[] (incl. username, fullName, bio, fulldate, views, description, preview.link, link)                                                                     | 2-5min   |
| Discord      | Apify actors             | action, channelUrl, minDelay, maxDelay                                       | messages[] (incl. content, author, timestamp, attachments[], mentions[], message_reference) or members[] (incl. username, displayName, roles[], joinedTimestamp) | 10-20min |

# **Rationale behind the Refresh Rates:**

### 1. Telegram (2-5min refresh)

**Rationale:**

* Telegram is often used for real-time crisis communication in sensitive regions
* Critical channels can update multiple times per minute during emergencies
* The platform has relatively lax scraping detection compared to others
* Shorter refresh rate is justified by Telegram's role in our threat detection system (Section 7.3)
* The 2-5 minute window captures urgent developments while avoiding excessive load

### 2. Instagram, X/Twitter, YouTube (2-10min refresh)

**Rationale:**

* These platforms have high content velocity but aggressive anti-scraping measures
* Instagram: Visual content spreads rapidly but platform detects frequent scraping (2-10min balances discovery vs. detection risk)
* Twitter/X: Real-time nature demands frequent checks, but API limits and anti-scraping require variability
* YouTube: Video content updates less frequently than tweets but comments drive urgency
* The variable window (2-10min) implements "adaptive pacing" (Section 3.2) to avoid detection while prioritizing high-impact content

### 3. TikTok, Bluesky (5-15min refresh)

**Rationale:**

* These platforms have dynamic content but moderate anti-scraping measures
* Content lifespan is longer than Twitter but shorter than LinkedIn
* The 5-15 minute window accounts for platform-specific API limitations
* Allows for "burst" processing during high-activity periods while maintaining baseline coverage
* Aligns with our social media freshness SLI of <10min (Section 12.3)

### 4. LinkedIn, Facebook, Reddit, Truth Social, Discord (10-20min refresh)

**Rationale:**

* These platforms have slower content velocity but stricter anti-scraping measures
* Professional networks (LinkedIn) have less frequent but higher-value updates
* Community platforms (Reddit, Discord) have longer discussion cycles
* The 10-20 minute window respects platform terms of service while capturing meaningful updates
* Longer intervals reduce infrastructure load for less time-sensitive content





### 3.2 Scraping Resilience and Fallbacks

- **Fallback tiering:**
  - Tier 1: Apify actor
  - Tier 2: Official API (tenant-owned credentials)
  - Tier 3: RSS/public mirrors or web archives
- **Anti-block strategies:** rotating IPs, adaptive pacing, randomized headers
- **Content safety gates:** respect robots.txt unless legal authorization; jurisdictional review

## 4. Data Model & Storage

### 4.1 Core Tables

```sql
-- Core content tables
documents (id, type, content_hash, source, published_at, location_id, tenant_id, deep_research_status)
sources (id, name, provider)
entities (id, doc_id, type, value, geo_scope)
sentiments (doc_id, polarity, stance, confidence)
clusters (id, topic, trend_score, doc_ids[])
reports (id, pdf_uri, dashboard_uri, confidence_score)
alerts (id, severity, status, rule_id, created_at)
locations (id, name, type, lat_lng, bounding_polygon, aliases)

-- Extended tables
claims (id, doc_id, text, label, sources[], corroboration_count, confidence, updated_at)
threat_opportunities (id, class, features_json, confidence, action_recommendation, 
                     related_entity_ids[], doc_ids[], location_id, created_at)
feedback_items (id, report_id, section, issue_type, severity, comment, status, 
               assignee, created_at, resolved_at)
branding_themes (tenant_id, version, logo_uri, palette_json, typography_json, 
                footer_text, created_at)
deep_research_results (id, doc_id, findings_json, summary, clusters[], confidence_score, processed_at)
```

### 4.2 Caching Strategy

- `feeds:{tenant}:{location_id}:{category}` (TTL: 2-5min)
  - Use-case: /feeds endpoint – served directly from Deep Research
- `trends:{tenant}:{location_id}` (TTL: 30min)
  - Use-case: /trends endpoint – topic clusters & scores; expensive TF-IDF + HDBSCAN computation
- `social:feed:{tenant}:{platform}:{location}` (TTL: 2-5min)
  - Use-case: Live dashboard widgets showing latest social media for specific locations
- `alerts:recent:{tenant}` (TTL: 5-10min)
  - Use-case: Web-PubSub push to browser/Teams – avoids re-querying PostgreSQL
- `search:{tenant}:{q_hash}` (TTL: 5-15min)
  - Use-case: /internet-search endpoint – identical queries from same tenant
- `deep-research:{tenant}:{location_id}:{content_hash}` (TTL: 5-15min)
  - Use-case: Caching Deep Research processing results to avoid redundant analysis



### Rational behind why Trends has (<30min refresh)

**Rationale:**

* Trend identification requires sufficient data points for statistical significance
* Shorter intervals produce noisy, unreliable trends (Section 4.2 shows trends cache has 30min TTL)
* The 30-minute window aligns with human perception of "emerging trends"
* Computational cost of TF-IDF + HDBSCAN (mentioned in Section 4.2) makes more frequent processing inefficient
* Balances timely detection with analytical accuracy

## 5. API Endpoints & Performance

### 5.1 Core Endpoints

| Endpoint             | Purpose                       | Latency Target   | Caching         |
| -------------------- | ----------------------------- | ---------------- | --------------- |
| POST /ingest/launch  | Trigger parallel ingestion    | 80-150ms         | Idempotency     |
| GET /feeds           | Combined news/social/web feed | 50-100ms (cache) | Redis           |
| GET /trends          | Topic clusters and scores     | 80-150ms         | Location-scoped |
| GET /internet-search | Tavily-based web search       | 20-40ms (cache)  | Country-level   |
| POST /reports        | Generate PDF/dashboard        | 3-15s            | No cache        |
| POST /alerts/test    | Validate alert rules          | 80-150ms         | No cache        |

### 5.2 Extended API Contracts

**Feeds Endpoint**

```
GET /feeds?location_id=loc_123&since=2025-09-15T00:00:00Z&platforms=x,tiktok&min_credibility=0.6
```

**Query Parameters:**

- location_id: string
- since, until: ISO timestamps
- sources[], platforms[]: arrays
- languages[]: array
- cursor: string (pagination)
- limit: int (max 100)
- sort: published_at|relevance

**Reports Endpoint**

```
POST /reports
{
    "tenant_id": "tenant_001",
    "template": "standard",
    "include_appendix": true,
    "sections_include": ["executive_summary", "threats", "trends"],
    "branding_version": "v2",
    "export": ["pdf", "dashboard_json"]
}
```

**Response:**

```
{
    "report_id": "rpt_123",
    "status": "processing",
    "pdf_uri": "https://...",
    "dashboard_uri": "https://...",
    "section_confidences": [{"section": "threats", "confidence": 0.87}],
    "build_id": "build_456"
}
```

### 5.3 Uniform Error Response

```
{
    "error": {
      "code": "validation_error",
      "message": "Invalid location parameter",
      "request_id": "req_123",
      "details": "Location ID must be in format loc_*"
  }
}
```

## 6. Hyperlocal Intelligence

### 6.1 Location Granularity Support

- Country: "Afghanistan"
- Region: "Kandahar Province"
- City: "Kandahar City"
- District: "District 9"
- Coordinate: `31.6289° N, 65.7372° E ± 5km`
- Custom Polygon: Irregular boundary support (Phase 2)

### 6.2 News and Vertical Coverage

- NewsAPI provides access to 8000+ global news sources including BBC, Bloomberg, Al Jazeera, and Reuters

### 6.3 Government and NGO Portals

- Target classes: ministry domains, municipal subdomains, gazettes, advisories, NGO/INGO field reports
- Discovery: curated domain lists + site-scoped search; freshness validation

### 6.4 Hyperlocal Source Examples (Kandahar City)

- News: Afghan local portals, BBC Pashto, municipal releases
- Social: `#Kandahar` hashtags, geotagged posts, local handles
- Web: Municipal websites, NGO pages, event listings
- Trends: `.af` domain content, Pashto/Dari language scoping

### 6.5 Location Processing Pipeline

1. Upstream AI Agent receives raw location input from user
2. Location validation and normalization using Azure Maps API to canonical `location_id`
3. Extract location from provider metadata/geotags
4. Store in locations table with hierarchical relationships
5. Index documents by `(tenant_id, location_id, published_at)`
6. Cache with location-scoped Redis keys

## 7. Deep Research Processing Pipeline

The Deep Research MCP serves as the central intelligence processing engine, handling all analysis:

### Document Processing

- Accepts raw document batches from Internet Search, News, and Social MCPs
- Content categorization using hierarchical topic taxonomy
- Deduplication: SimHash with 0.8 similarity threshold
- Prioritization based on recency, source credibility, and location relevance

### Content Analysis

- LangChain integration for content processing and semantic understanding
- Tavily for additional context and fact-checking
- Sentiment analysis using Azure Cognitive Services
- Cross-referencing across all source types for verification

### Confidence Scoring

- Assigns confidence scores to all findings (0.0-1.0 scale)
- Flags potential misinformation with "unverified_claim" tags
- Provides risk assessment for monitored regions
- Highlights emerging threats and opportunities with severity levels

### Output

- Structured findings with source attribution
- Confidence scores for each piece of information
- Prioritized threat/opportunity assessments
- Ready for Validation AI Agent processing

✓ All data now flows into Deep Research MCP as required
✓ LangChain and Tavily roles correctly defined for content processing
✓ Content categorization, deduplication, and prioritization explicitly attributed to Deep Research
✓ Confidence scoring now includes risk assessment and threat highlighting

### 7.3 Threat and Opportunity Classification with Deep Research MCP

**Taxonomy**

- Classes: operational disruption, civil unrest, policy change, infrastructure outage, public health, reputational risk, market opportunity, partnership window, community engagement

**Classification Workflow**

1. Initial detection by Deep Research MCP
2. Confidence scoring applied to each finding (7.4)
3. Risk assessment based on confidence score and impact potential
4. Threat/opportunity highlighting with severity levels:
   - Critical (confidence ≥ 0.85)
   - High (confidence 0.7-0.84)
   - Medium (confidence 0.5-0.69)
   - Low (confidence < 0.5)

**Classifier Design**

- Hybrid: rules + supervised model (LogReg/XGBoost)
- Outputs: class, confidence, rationale_snippet, related_entities[], recommended_action
- Integration: enrichment pipeline step with direct connection to Reporting MCP

✓ Explicit connection between confidence scores and threat classification
✓ Risk assessment methodology clearly defined
✓ Emerging threats/opportunities highlighted with severity levels

### 7.4 Confidence Scoring with Deep Research MCP

**Weighted composite of:**

- Research depth (35%)
- Cross-source corroboration (30%)
- Content recency (20%)
- Sentiment consistency (15%)

**Confidence Score Application**

- Each finding receives an individual confidence score
- Section-level confidence scores calculated as weighted averages
- Overall report confidence score derived from section scores
- Confidence scores drive threat/opportunity severity levels

## 8. Reporting and Dashboards

### 8.1 Standard Report Template

**Report Compilation Process**

1. Deep Research MCP compiles comprehensive intelligence from validated findings
2. Validation AI Agent performs final quality check
3. Reporting MCP formats the validated intelligence into deliverable formats

**Report Structure**

- Cover (title, logo, period, location)
- Executive summary (5-7 bullet insights with confidence scores)
- Key developments timeline with source attribution
- Threats/opportunities section with:
  - Classification and severity levels
  - Risk assessment based on confidence scores
  - Actionable recommendations
- Geospatial heat maps for location-based threats
- Sentiment/trends analysis with visualizations
- Source attribution table with credibility indicators
- Appendix (methods, limitations, glossary)

### 8.2 Report Formatting & Optimization

**Formatting Process**

- Deep Research MCP provides structured intelligence data to Reporting MCP
- Reporting MCP applies client specifications:
  - PDF, dashboard, or preferred delivery format
  - Executive summary with key insights
  - Actionable recommendations prioritized by confidence score
  - Data visualizations including geographical heat maps
  - Client branding elements (logo, color scheme, typography)

**Optimization Features**

- Confidence scores displayed as interactive meters
- Threat/opportunity highlights with color-coded severity
- Geospatial heat maps showing threat concentration
- Dynamic content prioritization based on client preferences

### 8.3 Final Delivery Methods

- Email (SendGrid integration)
- API endpoint retrieval
- Azure Static Web Apps dashboard access
- Optional real-time dashboard access for ongoing monitoring

✓ Deep Research MCP explicitly identified as compiler of intelligence report
✓ Executive summary, key insights, and actionable recommendations included
✓ Geographical heat maps explicitly connected to report formatting
✓ Confidence scores integrated throughout report structure

## 9. Async Processing & Orchestration

### 9.1 Service Bus Architecture

Upstream AI Agent (Location Validation) → Control Plane → Service Bus Topics (per MCP) → Durable Functions → Fan-out → Parallel Processing → Fan-in → Deep Research Processing → Validation AI Agent → Reporting MCP → Upstream AI Agent (Delivery) → Final Delivery Channels

**Upstream AI Agent Responsibilities**

- Receives user request and validates location at workflow initiation
- Normalizes location using Azure Maps API to canonical `location_id`
- Checks location against supported granularity levels
- Verifies location exists in the locations table with hierarchical relationships
- Returns standardized location object with bounding polygon for downstream processing
- Retrieves completed and validated reports from Reporting MCP
- Determines optimal delivery method based on client preferences
- Manages delivery channel selection (email, API, dashboard)
- Handles real-time dashboard access provisioning
- Monitors delivery status and initiates retries for failures

### 9.2 Retry & Error Handling

- Retries for each tool with configurable parameters:
  - Max attempts (3-5 based on component)
  - Exponential backoff with jitter
  - Transaction-specific logging for each retry
  - Dead letter queues for unprocessable messages
  - Circuit breakers to prevent cascading failures

### 9.3 Rate Limits and Circuit Breaking

- Rate budgets: dynamic token buckets per provider
- Backoff: exponential with jitter, max 5 attempts
- Circuit breaker: closed → open at threshold; half-open probes every 60s

### 9.4 Background Schedulers

- News/Social: Timer-triggered Functions per refresh cadence
- Deep Research: Hourly batch processing
- Compaction: Daily cleanup jobs
- Cache warming: Preload popular location/trend data; precompute cluster summaries

**Note:** LangChain + Tavily will be used exclusively for Deep Research operations. Tavily alone handles basic web search.

### 9.5 MCP Server Concurrency Model – Detailed Rules

| Decision Point           | Mandatory Choice                                                                                                                                                                                    | Rationale                                                                                                                                       |
| ------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------- |
| 1. Latency budget check  | ≤ 500 ms end-to-end → Sync OK                                                                                                                                                                       | 500 ms is the upper bound in the published SLIs (§ 12.3). Staying under it keeps the gateway thread-pool from exhaustion and the UI responsive. |
| 2. External actor SLA    | If any external actor (scraper, deep-research, PDF render, Tavily, Apify, YouTube, etc.) advertises > 30 s wall-clock, never wait synchronously.                                                    | Actor queues can be minutes deep; waiting blocks the gateway and violates the 99.9% API availability SLO.                                       |
| 3. Fan-out > 1           | Control-Plane must drop a Service Bus message and immediately return HTTP 202 + operationId.                                                                                                        | Fan-out (§ 9.1) guarantees parallel execution; a sync call would be held by the slowest worker and adds up latencies serially.                  |
| 4. Idempotency & retries | Async path must store an idempotency key (Redis, 24 h TTL) so re-POSTs return the same operationId without duplicate work.                                                                          | Prevents users from accidentally spawning multiple long-running jobs for an identical request.                                                  |
| 5. Result retrieval      | Provide two tool signatures in the MCP manifest:                                                                                                                                                    | Let's UI choose poll or WebSocket while the server remains stateless.                                                                           |
| 6. Cache-hit fast path   | If the answer is already in Redis and Redis latency ≤ 2 ms, skip async and return 200 OK immediately.                                                                                               | Keeps hot reads (feeds, trends, search cache) on the sync path without violating rule 1.                                                        |
| 7. Time-out ceiling      | Async jobs must enforce an absolute time-out (15 min for reports, 15 min for deep-research processing). After time-out the job is cancelled, status set to failed, and dead-letter message written. | Prevents run-away tasks from consuming workers forever.                                                                                         |
| 8. Observability         | Emit two log entries per job:                                                                                                                                                                       | Required for the per-MCP dashboards (§ 12.2) and for SLI calculation (success rate, p95 duration).                                              |

✓ Upstream AI Agent added to beginning of workflow for location validation
✓ Upstream AI Agent responsibilities expanded to include initial validation
✓ Clear responsibilities defined for report retrieval and delivery
✓ Final delivery channels properly integrated

## 10. Quality Assurance & Validation

### 10.5 Quality Assurance & Validation Workflow

**Validation AI Agent Process**

- Retrieves completed report from Reporting MCP
- Performs comprehensive quality check:
  - Data accuracy verification against source materials
  - Source credibility assessment using established metrics
  - Completeness check against required intelligence parameters
  - Gap analysis for missing critical information
- Generates validation_results with:
  - Quality score (0-100)
  - Identified issues with severity levels
  - Specific recommendations for improvement

**Validation Criteria**

- Accuracy: ≥95% of claims supported by credible sources
- Completeness: All required intelligence dimensions covered
- Timeliness: Data freshness within defined thresholds
- Actionability: Clear recommendations provided for all findings

**Validation Failure Handling**

- Automatic retry for minor issues
- Escalation to human review for major discrepancies
- Report generation halted until validation passes

✓ Dedicated Validation AI Agent workflow added
✓ All quality assurance requirements explicitly covered
✓ Clear validation criteria and failure handling procedures

## 12. Observability & Monitoring

### 12.1 Logging Standards

```
{
    "timestamp": "2025-01-15T10:30:00Z",
    "request_id": "req_abc123",
    "correlation_id": "corr_xyz789",
    "tenant_id": "tenant_001",
    "component": "news_mcp",
    "latency_ms": 245,
    "status": "success"
}
```

### 12.2 Monitoring Stack

**Transaction Logging**

- All logs published directly for each transaction with:
  - Transaction ID
  - MCP component
  - Start/end timestamps
  - Status (success/failure)
  - Error details (if applicable)
  - Confidence score (for analysis transactions)
- Logs routed to Splunk with dedicated transaction views

**Fault Tolerance Implementation**

- Retries for each tool with configurable parameters:
  - Max attempts (3-5 based on component)
  - Exponential backoff with jitter
  - Transaction-specific logging for each retry
  - Dead letter queues for unprocessable messages
  - Circuit breakers to prevent cascading failures

**Notification System**

- Real-time alerts for potential issues:
  - MCP failures (immediate notification)
  - High error rates (>5% threshold)
  - Performance degradation (p95 latency breaches)
  - Data quality issues (confidence score drops)
- Admin notifications routed to:
  - Slack/Teams channels (operational issues)
  - PagerDuty (critical system failures)
  - Email digests (daily summary reports)

### 12.3 SLI/SLO Definitions

- API Availability: 99.9% uptime
- Latency p95: Feed APIs <100ms, Search APIs <500ms
- Data Freshness: News <5min, Social <10min, Trends <30min
- Alert TTR: High severity <5min, Medium <30min
  
  
  
  
  
  Data Freshness Rationale:
  - News <5min: Balances breaking news requirements with API rate limits
  - Social <10min: Accounts for platform-specific content velocity and anti-scraping constraints
  - Trends <30min: Ensures statistical significance of trend analysis while maintaining relevance
  - Critical threat indicators: <2min (new category for high-severity locations)

### 12.4 Alerting Configuration

**Thresholds:**

- Error rate: >1% over 5min window
- Queue depth: >1000 messages for >10min
- External API failures: >50% failure rate over 2min
- Database connections: >80% pool utilization

**Destinations:**

- email (SendGrid), Slack/Teams, PagerDuty
- Routing: error rate/queue depth → Slack; API failures → PagerDuty

✓ All transaction logs published directly as required
✓ Fault tolerance details include per-transaction logging
✓ Monitoring stack explicitly uses Splunk for transaction logs
✓ Notification system covers all potential issues with admin alerts

# 13. GitHub Actions Deployment Pipeline Strategy for Project Geospatial (Proposed)

## Pipeline Architecture Overview

Here's a proposed GitHub Actions deployment pipeline strategy that aligns with Project Geospatial's documented architecture:

### **Phase 1: Code Validation & Build**

1. **Trigger Conditions**
   
   - Push to `main` branch (production deployments)
   - Pull requests to `main` (staging deployments)
   - Scheduled nightly builds (infrastructure validation)

2. **Pre-Commit Checks**
   
   - Python linting with pre-commit hooks (aligns with FastAPI/Python ecosystem preference)
   - Type checking for all MCP server components
   - Security scanning for secrets (critical given Azure Key Vault usage)
   - Bicep template validation (supports documented "Bicep over Terraform" decision)

3. **Build Process**
   
   - Containerize FastAPI application (as documented in Section 2.1)
   - Package Azure Functions for Deep Research and other MCP components
   - Generate build artifacts with versioned tags (build_id for traceability as referenced in Section 5.2)

### **Phase 2: Testing & Validation**

1. **Automated Testing**
   
   - Unit tests for MCP server components (News, Social, Internet Search, Deep Research)
   - Integration tests validating Service Bus message flow between components (Section 9.1)
   - End-to-end tests simulating core workflow: "User Request → Upstream AI Agent → API Gateway → Service Bus Fan-out → Parallel MCPs → Deep Research → Validation AI Agent → Report Generation" (Section 1)

2. **Performance Validation**
   
   - Latency testing against documented targets (Section 5.1):
     * `/feeds`: 50-100ms
     * `/trends`: 80-150ms 
     * `/internet-search`: 20-40ms
     * `/reports`: 3-15s
   - Load testing to validate auto-scaling behavior (2-10 instances as documented in Section 2.1)

3. **Security Validation**
   
   - Dependency scanning for vulnerabilities (critical for external API integrations)
   - RBAC validation for PostgreSQL tenant isolation (Section 2.3)
   - JWT authentication testing with Azure AD B2C (Section 2.1)

### **Phase 3: Staging Deployment**

1. **Infrastructure Provisioning**
   
   - Deploy infrastructure using Azure Bicep templates (per documented "Bicep over Terraform" decision)
   - Create isolated staging environment with all required components:
     * Azure App Service (Control Plane)
     * PostgreSQL database with RLS
     * Redis cache
     * Service Bus namespace
     * Blob Storage containers
     * Web PubSub instance

2. **Application Deployment**
   
   - Deploy containerized FastAPI application to staging App Service
   - Deploy Azure Functions for MCP components to staging environment
   - Configure Service Bus topics and subscriptions per documented architecture (Section 9.1)

3. **Validation Testing**
   
   - Smoke tests for all core endpoints (Section 5.1)
   - Data pipeline validation: verify News/Social data ingestion at documented refresh rates (Section 3.1)
   - Deep Research processing validation with sample inputs
   - Validation AI Agent quality check simulation

### **Phase 4: Production Deployment**

1. **Deployment Strategy**
   
   - **Blue-Green Deployment** using Azure App Service slots (supports documented 99.9% uptime SLI)
   - Deploy new version to "green" slot while "blue" slot continues serving traffic
   - Run production validation tests against the green slot

2. **Automated Promotion Criteria**
   
   - All staging validation tests passing
   - No SLI violations during staging testing (API Availability, Latency p95, Data Freshness per Section 12.3)
   - Validation AI Agent quality score ≥ 95 (per Section 10.5)

3. **Traffic Switching**
   
   - Slot swap with warm-up period to ensure all components are ready
   - Post-swap monitoring for:
     * Error rate (<1% threshold per Section 12.4)
     * Queue depth (<1000 messages per Section 12.4)
     * External API success rate (>50% threshold per Section 12.4)

4. **Rollback Protocol**
   
   - Automated rollback triggered if:
     * Error rate exceeds 5% within 5 minutes
     * Queue depth exceeds 1000 messages for >10 minutes
     * API latency exceeds documented targets by 2x
   - Manual approval gate for critical production deployments

### **Phase 5: Post-Deployment Operations**

1. **Observability Integration**
   
   - Verify Splunk logging is functioning (per documented "Splunk for transaction logging" decision)
   - Validate monitoring dashboards showing:
     * Transaction views per Section 12.2
     * SLI/SLO compliance per Section 12.3
     * Alert thresholds per Section 12.4

2. **Cache Warming**
   
   - Execute documented "Cache warming" process (Section 9.4)
   - Preload popular location/trend data
   - Precompute cluster summaries for high-traffic locations

3. **Background Scheduler Validation**
   
   - Verify timer-triggered Functions are running per documented schedules:
     * News/Social: per refresh cadence (Section 3.1)
     * Deep Research: Hourly batch processing (Section 9.4)
     * Compaction: Daily cleanup jobs (Section 9.4)

### **Pipeline Governance**

1. **Approval Workflows**
   
   - Required approvals for production deployments:
     * Platform lead (1 approval)
     * Security officer (1 approval for infrastructure changes)
     * QA lead (1 approval for code changes)

2. **Audit Trail**
   
   - All pipeline executions linked to:
     * Git commit SHA
     * Build ID (as referenced in Section 5.2)
     * Request ID for traceability
   - Full audit log of infrastructure changes via Bicep deployments

3. **Compliance Verification**
   
   - Automated checks for:
     * Tenant isolation in PostgreSQL (Section 2.3)
     * Data retention policies
     * Security scanning results
       
       

# Appendix: Decision Log

| Decision                                                       | Rationale                                                                     |
| -------------------------------------------------------------- | ----------------------------------------------------------------------------- |
| Azure-first architecture                                       | Client preference, native integration, enterprise support                     |
| PostgreSQL over Cosmos DB                                      | Relational data model, ACID guarantees, cost efficiency                       |
| Service Bus over Event Grid                                    | Message durability, retry policies, ordered processing                        |
| Redis over Azure Cache                                         | Performance requirements, data structure support                              |
| Durable Functions over Logic Apps                              | Complex orchestration, custom retry logic, cost control                       |
| FastAPI over .NET Core                                         | Python ecosystem, rapid development, team expertise                           |
| Bicep over Terraform                                           | Azure native, better integration, simpler syntax                              |
| Internet Search: Tavily API only                               | Separates basic web search from complex research tasks                        |
| Deep Research: LangChain + Tavily as primary processing engine | Consolidates all intelligence processing into a single, more capable pipeline |
| Upstream AI Agent location validation                          | Ensures accurate geospatial processing at workflow initiation                 |
| Splunk for transaction logging                                 | Preferred by client for enterprise monitoring needs                           |
| Validation AI Agent implementation                             | Added to ensure end-to-end quality control before report delivery             |
