# README.md
# Geospatial NewsAPI MCP Server (FastMCP)

A modern, simple NewsAPI MCP server built with **FastMCP** for the Geospatial project. Uses Python type hints, Pydantic validation, and clean decorators for maximum developer productivity.

## Features

- ** search-geospatial-news**: Search articles with keyword filtering
- ** get-regional-headlines**: Get headlines by country/category  
- ** get-news-sources**: Browse available news sources
- ** news-api-info**: Resource with API information and limits

## Quick Start

### Prerequisites
- Python 3.11+
- NewsAPI key (free at [newsapi.org](https://newsapi.org))

### Installation
```bash
# Install dependencies
pip install -r requirements.txt

## Paste your News API key paste in a .env file"

## Next Steps
 * Add Azure Service Bus integration
 * Implement Redis caching
 * Add location-aware filtering
 * Deploy to Azure App Service