name: Deploy Hello.txt

on:
  push:
    paths:
      - Hello.txt

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - uses: actions/checkout@v4
      - name: Simulate Deploy to Staging
        run: echo "Deployed Hello.txt to STAGING"

  deploy-production:
    needs: deploy-staging
    runs-on: ubuntu-latest
    environment:
      name: production
      # This enforces manual approval in GitHub UI if you set up a reviewer
    steps:
      - uses: actions/checkout@v4
      - name: Simulate Deploy to Production
        run: echo "Deployed Hello.txt to PRODUCTION"