#!/usr/bin/env python3
"""
Launcher script for the Geospatial NewsAPI MCP Server with FastMCP 2.x
"""
import os
import sys
from dotenv import load_dotenv

def main():
    """Launch the NewsAPI MCP Server with proper environment setup"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if API key is configured
    if not os.getenv("NEWSAPI_APIKEY"):
        print("Error: NEWSAPI_APIKEY environment variable is not set")
        print("Please add your NewsAPI key to the .env file:")
        print("NEWSAPI_APIKEY=your_api_key_here")
        sys.exit(1)
    
    print("Starting Geospatial NewsAPI MCP Server...")
    print(f"API Key configured: {os.getenv('NEWSAPI_APIKEY')[:8]}...")
    print("FastMCP configuration: fastmcp.json")
    
    # Import and run the server
    try:
        from news_server import mcp
        
        # Run with FastMCP 2.x
        print("Server starting with FastMCP 2.x...")
        mcp.run(transport="http", host="127.0.0.1", port=8000)
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure you've installed the dependencies:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()