# test_social_media_server.py
import asyncio
import os
import pytest
from unittest.mock import patch, AsyncMock, MagicMock

# Setup test environment
def setup_test_env():
    if not os.getenv("APIFY_API_TOKEN"):
        os.environ["APIFY_API_TOKEN"] = "test_token_for_testing"

setup_test_env()

from social_media_server import (
    InstagramSearchRequest, 
    YouTubeSearchRequest,
    format_instagram_results,
    format_youtube_results,
    make_apify_request
)

# Mock data
MOCK_INSTAGRAM_DATA = [
    {
        "type": "post",
        "caption": "Beautiful sunset at the beach! #sunset #nature",
        "ownerUsername": "testuser",
        "likesCount": 150,
        "commentsCount": 25,
        "displayUrl": "https://example.com/image.jpg",
        "locationName": "Miami Beach",
        "timestamp": "2025-01-15T18:30:00Z"
    }
]

MOCK_YOUTUBE_DATA = [
    {
        "title": "Amazing Travel Vlog - Exploring Paradise",
        "channel_info": {"name": "TravelWithMe"},
        "view_count": 50000,
        "like_count": 2500,
        "duration": "10:35",
        "description": "Join me as I explore the most beautiful destinations...",
        "url": "https://youtube.com/watch?v=example",
        "upload_date": "2025-01-15"
    }
]

@pytest.mark.asyncio
async def test_instagram_request_validation():
    """Test Instagram request model validation"""
    request = InstagramSearchRequest(
        search_terms=["travel", "adventure"],
        search_type="hashtag",
        result_limit=10
    )
    
    assert request.search_terms == ["travel", "adventure"]
    assert request.search_type == "hashtag"
    assert request.result_limit == 10

@pytest.mark.asyncio
async def test_youtube_request_validation():
    """Test YouTube request model validation"""
    request = YouTubeSearchRequest(
        search_queries=["travel vlog", "adventure"],
        max_results=15,
        include_comments=True
    )
    
    assert request.search_queries == ["travel vlog", "adventure"]
    assert request.max_results == 15
    assert request.include_comments == True

@pytest.mark.asyncio
async def test_format_instagram_results():
    """Test Instagram results formatting"""
    result = format_instagram_results(MOCK_INSTAGRAM_DATA, ["sunset"])
    
    assert "Instagram Results" in result
    assert "testuser" in result
    assert "Beautiful sunset" in result
    assert "Miami Beach" in result
    assert "150" in result  # likes count

@pytest.mark.asyncio
async def test_format_youtube_results():
    """Test YouTube results formatting"""
    result = format_youtube_results(MOCK_YOUTUBE_DATA, ["travel"])
    
    assert "YouTube Results" in result
    assert "Amazing Travel Vlog" in result
    assert "TravelWithMe" in result
    assert "50,000" in result  # view count formatted

@pytest.mark.asyncio
async def test_apify_request_mock():
    """Test Apify API request with mocking"""
    with patch('social_media_server.httpx.AsyncClient') as mock_client:
        # Mock the async context manager and responses
        mock_client_instance = AsyncMock()
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Mock start response
        start_response = MagicMock()
        start_response.json.return_value = {"data": {"id": "test_run_123"}}
        start_response.raise_for_status = MagicMock()
        
        # Mock status response
        status_response = MagicMock()
        status_response.json.return_value = {"data": {"status": "SUCCEEDED"}}
        
        # Mock results response
        results_response = MagicMock()
        results_response.json.return_value = MOCK_INSTAGRAM_DATA
        results_response.raise_for_status = MagicMock()
        
        # Setup call sequence
        mock_client_instance.post.return_value = start_response
        mock_client_instance.get.side_effect = [status_response, results_response]
        
        # Test the function
        result = await make_apify_request("test/actor", {"test": "data"})
        
        assert result == MOCK_INSTAGRAM_DATA

if __name__ == "__main__":
    print("Running Social Media MCP Server Tests...")
    pytest.main([__file__, "-v", "--asyncio-mode=auto", "--tb=short"])